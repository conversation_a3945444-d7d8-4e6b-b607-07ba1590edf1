import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Mail, Phone, User, GraduationCap, School, Calendar, MapPin, Clock, CheckCircle, Award, Shield } from "lucide-react";
import cyberwolfLogo from "@/assets/cyberwolf-logo.png";
import speaker1 from "@/assets/speaker1.png";
import speaker2 from "@/assets/speaker2.png";

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  collegeName: z.string().optional(),
  degree: z.string().optional(),
  school: z.string().min(2, "School name is required"),
  age: z.string().min(1, "Age is required").refine((val) => {
    const age = parseInt(val);
    return age >= 16 && age <= 50;
  }, "Age must be between 16 and 50"),
  contactNumber: z.string().min(10, "Contact number must be at least 10 digits").regex(/^[+]?[0-9\s-()]+$/, "Please enter a valid phone number"),
  email: z.string().email("Please enter a valid email address"),
  experience: z.string().optional(),
  motivation: z.string().optional(),
  referralSource: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

const Registration = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);
  const { toast } = useToast();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      collegeName: "",
      degree: "",
      school: "",
      age: "",
      contactNumber: "",
      email: "",
      experience: "",
      motivation: "",
      referralSource: "",
    },
  });

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    
    try {
      // Create detailed email content
      const emailBody = `
NEW COURSE REGISTRATION - WEB PENETRATION TESTING COURSE

======================================
PERSONAL INFORMATION
======================================
Name: ${data.name}
Age: ${data.age}
Email: ${data.email}
Contact Number: ${data.contactNumber}

======================================
EDUCATIONAL BACKGROUND
======================================
School: ${data.school}
College: ${data.collegeName || "Not provided"}
Degree: ${data.degree || "Not provided"}

======================================
ADDITIONAL INFORMATION
======================================
Previous Experience: ${data.experience || "Not provided"}
Motivation: ${data.motivation || "Not provided"}
How did you hear about us: ${data.referralSource || "Not provided"}

======================================
COURSE DETAILS
======================================
Course: FREE WEB PENETRATION TESTING COURSE
Date: 18th July 2025 (Friday)
Time: 11:00 AM – 3:00 PM (IST)
Mode: Online

Registration Time: ${new Date().toLocaleString()}
      `;

      // Create mailto link
      const mailtoLink = `mailto:<EMAIL>?subject=Course Registration - ${data.name} - Web Penetration Testing&body=${encodeURIComponent(emailBody)}`;
      
      // Open email client
      window.open(mailtoLink);
      
      // Show success state
      setIsRegistered(true);
      
      toast({
        title: "Registration Successful!",
        description: "Your registration email has been prepared. Please send it to complete your registration.",
      });

      // Auto scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });

    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isRegistered) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT flex items-center justify-center px-6">
        <Card className="w-full max-w-lg mx-auto glassmorphism text-center">
          <CardContent className="pt-8 pb-8">
            <div className="mb-6">
              <CheckCircle className="mx-auto text-psyco-green-DEFAULT mb-4" size={64} />
              <h2 className="text-2xl font-bold text-white mb-2">Registration Successful!</h2>
              <p className="text-gray-300 mb-4">
                Your registration email has been prepared and opened in your default email client.
              </p>
              <div className="bg-psyco-green-DEFAULT/10 border border-psyco-green-DEFAULT/30 rounded-lg p-4 mb-6">
                <p className="text-white text-sm">
                  <strong>Next Steps:</strong><br />
                  1. Check your email client<br />
                  2. Send the pre-filled email<br />
                  3. Wait for confirmation
                </p>
              </div>
              <Button 
                onClick={() => setIsRegistered(false)}
                variant="outline"
                className="border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT hover:text-white"
              >
                Register Another Person
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT py-12 px-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <img 
              src={cyberwolfLogo} 
              alt="Cyber Wolf Logo" 
              className="w-24 h-24 object-contain animate-fade-in"
            />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4 animate-fade-in">
            Course Registration
          </h1>
          <p className="text-psyco-green-DEFAULT text-xl font-medium mb-8 animate-fade-in">
            FREE WEB PENETRATION TESTING COURSE
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Course Information */}
          <div className="space-y-6">
            {/* Course Details Card */}
            <Card className="glassmorphism">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-3">
                  <Shield className="text-psyco-green-DEFAULT" size={24} />
                  Course Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-3 text-gray-300">
                    <Calendar className="text-psyco-green-DEFAULT" size={16} />
                    <span>18th July 2025 (Friday)</span>
                  </div>
                  <div className="flex items-center gap-3 text-gray-300">
                    <Clock className="text-psyco-green-DEFAULT" size={16} />
                    <span>11:00 AM – 3:00 PM (IST)</span>
                  </div>
                  <div className="flex items-center gap-3 text-gray-300">
                    <MapPin className="text-psyco-green-DEFAULT" size={16} />
                    <span>Online Mode</span>
                  </div>
                  <div className="flex items-center gap-3 text-gray-300">
                    <Award className="text-psyco-green-DEFAULT" size={16} />
                    <span>Free Certificate</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Topics Covered */}
            <Card className="glassmorphism">
              <CardHeader>
                <CardTitle className="text-white">What You'll Learn</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 text-gray-300">
                  {[
                    "Web Penetration Testing Fundamentals",
                    "OWASP Top 10 Vulnerabilities",
                    "Real-time Hacking Demonstrations",
                    "Industry Tools: Burp Suite, Nmap, Nikto",
                    "Ethical Hacking Methodologies",
                    "Security Assessment Techniques"
                  ].map((topic, index) => (
                    <li key={index} className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-psyco-green-DEFAULT rounded-full"></div>
                      {topic}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Speakers */}
            <Card className="glassmorphism">
              <CardHeader>
                <CardTitle className="text-white text-center">Course Instructors</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 text-center mb-6">Cyber Wolf Security Research Team</p>
                <div className="flex justify-center gap-8">
                  <div className="text-center">
                    <img 
                      src={speaker1} 
                      alt="Senior Security Researcher" 
                      className="w-20 h-20 rounded-full object-cover mx-auto mb-3 border-2 border-psyco-green-DEFAULT"
                    />
                    <p className="text-white text-sm font-medium">Security Expert</p>
                    <p className="text-gray-400 text-xs">Lead Researcher</p>
                  </div>
                  <div className="text-center">
                    <img 
                      src={speaker2} 
                      alt="Penetration Testing Specialist" 
                      className="w-20 h-20 rounded-full object-cover mx-auto mb-3 border-2 border-psyco-green-DEFAULT"
                    />
                    <p className="text-white text-sm font-medium">Penetration Tester</p>
                    <p className="text-gray-400 text-xs">Security Specialist</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card className="glassmorphism">
              <CardHeader>
                <CardTitle className="text-white">Contact Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-gray-300">
                  <div className="flex items-center gap-3">
                    <Mail className="text-psyco-green-DEFAULT" size={16} />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="text-psyco-green-DEFAULT" size={16} />
                    <span>+91 6374 344 424</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="text-psyco-green-DEFAULT" size={16} />
                    <span>+91 6379 869 678</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Registration Form */}
          <div className="lg:sticky lg:top-6">
            <Card className="glassmorphism">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-white">Register Now</CardTitle>
                <CardDescription className="text-gray-300">
                  Fill out the form below to secure your spot
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    {/* Personal Information */}
                    <div className="space-y-4">
                      <h3 className="text-white font-semibold border-b border-psyco-green-DEFAULT/30 pb-2">
                        Personal Information
                      </h3>
                      
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white flex items-center gap-2">
                              <User size={16} />
                              Full Name *
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter your full name"
                                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="age"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white flex items-center gap-2">
                                <Calendar size={16} />
                                Age *
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="Age"
                                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="contactNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white flex items-center gap-2">
                                <Phone size={16} />
                                Contact *
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="tel"
                                  placeholder="Phone number"
                                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white flex items-center gap-2">
                              <Mail size={16} />
                              Email Address *
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="Enter your email address"
                                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Educational Background */}
                    <div className="space-y-4">
                      <h3 className="text-white font-semibold border-b border-psyco-green-DEFAULT/30 pb-2">
                        Educational Background
                      </h3>
                      
                      <FormField
                        control={form.control}
                        name="school"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white flex items-center gap-2">
                              <School size={16} />
                              School/Institution *
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter your school/institution name"
                                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="collegeName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white flex items-center gap-2">
                                <GraduationCap size={16} />
                                College (Optional)
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="College name"
                                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="degree"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">Degree (Optional)</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger className="bg-white/10 border-white/20 text-white">
                                    <SelectValue placeholder="Select degree" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="btech">B.Tech</SelectItem>
                                  <SelectItem value="mtech">M.Tech</SelectItem>
                                  <SelectItem value="bca">BCA</SelectItem>
                                  <SelectItem value="mca">MCA</SelectItem>
                                  <SelectItem value="bsc">B.Sc Computer Science</SelectItem>
                                  <SelectItem value="msc">M.Sc Computer Science</SelectItem>
                                  <SelectItem value="diploma">Diploma</SelectItem>
                                  <SelectItem value="other">Other</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Additional Information */}
                    <div className="space-y-4">
                      <h3 className="text-white font-semibold border-b border-psyco-green-DEFAULT/30 pb-2">
                        Additional Information (Optional)
                      </h3>
                      
                      <FormField
                        control={form.control}
                        name="experience"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">Previous Experience in Cybersecurity</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Tell us about any previous experience in cybersecurity, programming, or related fields..."
                                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 resize-none"
                                rows={3}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="motivation"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">Why are you interested in this course?</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Share your motivation and what you hope to learn..."
                                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 resize-none"
                                rows={3}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="referralSource"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">How did you hear about us?</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="bg-white/10 border-white/20 text-white">
                                  <SelectValue placeholder="Select source" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="social-media">Social Media</SelectItem>
                                <SelectItem value="friend-referral">Friend/Colleague Referral</SelectItem>
                                <SelectItem value="college">College/University</SelectItem>
                                <SelectItem value="search-engine">Search Engine</SelectItem>
                                <SelectItem value="cybersecurity-community">Cybersecurity Community</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-3 text-lg btn-glow"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Processing Registration..." : "Register for Free Course"}
                    </Button>

                    <p className="text-xs text-gray-400 text-center">
                      By registering, you agree to receive course-related communications from Cyber Wolf.
                    </p>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Registration;