import React, { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Monitor, Calendar, Clock, Shield, Award, Users, ArrowRight, ExternalLink, BookOpen, Target, Code, Zap } from "lucide-react";
import cyberwolfLogo from "@/assets/cyberwolf-logo.png";
import speaker1 from "@/assets/speaker1.png";
import speaker2 from "@/assets/speaker2.png";

const Index = () => {
  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-6 md:px-12">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/10 rounded-full blur-3xl top-1/4 left-1/4 animate-pulse"></div>
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/5 rounded-full blur-3xl bottom-1/4 right-1/4 animate-pulse"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center">
            {/* Logo */}
            <div className="flex justify-center mb-8">
              <img 
                src={cyberwolfLogo} 
                alt="Cyber Wolf Logo" 
                className="w-32 h-32 object-contain animate-fade-in"
              />
            </div>
            
            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 animate-fade-in">
              FREE WEB PENETRATION
              <span className="block text-psyco-green-DEFAULT">TESTING COURSE</span>
            </h1>
            
            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-300 mb-8 animate-fade-in">
              Master Ethical Hacking & Cybersecurity with Industry Experts
            </p>
            
            {/* Course Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
              <div className="glassmorphism p-6 rounded-xl animate-fade-in">
                <Monitor className="text-psyco-green-DEFAULT mx-auto mb-3" size={32} />
                <h3 className="text-white font-bold mb-2">Online Mode</h3>
                <p className="text-gray-400 text-sm">Join from anywhere</p>
              </div>
              <div className="glassmorphism p-6 rounded-xl animate-fade-in">
                <Calendar className="text-psyco-green-DEFAULT mx-auto mb-3" size={32} />
                <h3 className="text-white font-bold mb-2">18th July 2025</h3>
                <p className="text-gray-400 text-sm">Friday</p>
              </div>
              <div className="glassmorphism p-6 rounded-xl animate-fade-in">
                <Clock className="text-psyco-green-DEFAULT mx-auto mb-3" size={32} />
                <h3 className="text-white font-bold mb-2">11:00 AM – 3:00 PM</h3>
                <p className="text-gray-400 text-sm">IST (4 Hours)</p>
              </div>
            </div>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in">
              <Link
                to="/registration"
                className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 flex items-center justify-center text-lg btn-glow"
              >
                Register Now - FREE
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <a
                href="mailto:<EMAIL>"
                className="bg-transparent border-2 border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT hover:text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 flex items-center justify-center text-lg"
              >
                Contact Us
                <ExternalLink className="ml-2 h-5 w-5" />
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* What You'll Learn Section */}
      <section className="py-20 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">What You'll Master</h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Comprehensive hands-on training in web penetration testing and ethical hacking techniques
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Shield size={40} />,
                title: "Web Penetration Testing Fundamentals",
                description: "Learn the core principles and methodologies of ethical hacking"
              },
              {
                icon: <Target size={40} />,
                title: "OWASP Top 10 Vulnerabilities",
                description: "Deep dive into the most critical web application security risks"
              },
              {
                icon: <Code size={40} />,
                title: "Industry-Standard Tools",
                description: "Master Burp Suite, Nmap, Nikto, and other professional tools"
              },
              {
                icon: <Zap size={40} />,
                title: "Real-time Hacking Demos",
                description: "Watch live demonstrations of actual penetration testing scenarios"
              },
              {
                icon: <BookOpen size={40} />,
                title: "Ethical Hacking Techniques",
                description: "Learn responsible disclosure and ethical hacking practices"
              },
              {
                icon: <Award size={40} />,
                title: "Professional Certificate",
                description: "Receive a certificate of completion to boost your career"
              }
            ].map((feature, index) => (
              <div 
                key={index}
                className="glassmorphism p-8 rounded-xl text-center hover:scale-105 transition-transform duration-300 animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="text-psyco-green-DEFAULT mb-4 flex justify-center">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-4">{feature.title}</h3>
                <p className="text-gray-400">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Instructors Section */}
      <section className="py-20 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">Meet Your Instructors</h2>
            <p className="text-xl text-gray-400">
              Learn from experienced cybersecurity professionals at Cyber Wolf
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto">
            <div className="glassmorphism p-8 rounded-xl text-center animate-fade-in">
              <img 
                src={speaker1} 
                alt="Senior Security Researcher" 
                className="w-32 h-32 rounded-full object-cover mx-auto mb-6 border-4 border-psyco-green-DEFAULT"
              />
              <h3 className="text-2xl font-bold text-white mb-2">Senior Security Expert</h3>
              <p className="text-psyco-green-DEFAULT font-medium mb-4">Lead Security Researcher</p>
              <p className="text-gray-400">
                Specialized in web application security with 8+ years of experience in penetration testing and vulnerability assessment.
              </p>
            </div>
            
            <div className="glassmorphism p-8 rounded-xl text-center animate-fade-in">
              <img 
                src={speaker2} 
                alt="Penetration Testing Specialist" 
                className="w-32 h-32 rounded-full object-cover mx-auto mb-6 border-4 border-psyco-green-DEFAULT"
              />
              <h3 className="text-2xl font-bold text-white mb-2">Penetration Testing Specialist</h3>
              <p className="text-psyco-green-DEFAULT font-medium mb-4">Security Research Team</p>
              <p className="text-gray-400">
                Expert in ethical hacking methodologies with extensive experience in security auditing and vulnerability research.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">Why Choose This Course?</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <Award size={48} />,
                title: "100% Free",
                description: "No hidden fees or charges"
              },
              {
                icon: <Users size={48} />,
                title: "Expert Instructors",
                description: "Learn from industry professionals"
              },
              {
                icon: <Shield size={48} />,
                title: "Hands-on Training",
                description: "Practical, real-world scenarios"
              },
              {
                icon: <BookOpen size={48} />,
                title: "Certificate Included",
                description: "Professional completion certificate"
              }
            ].map((benefit, index) => (
              <div 
                key={index}
                className="glassmorphism p-8 rounded-xl text-center animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="text-psyco-green-DEFAULT mb-4 flex justify-center">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{benefit.title}</h3>
                <p className="text-gray-400">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact & Registration CTA */}
      <section className="py-20 px-6 md:px-12 relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/10 rounded-full blur-3xl top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Ready to Start Your Cybersecurity Journey?</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Join thousands of students who have started their cybersecurity careers with our expert-led courses.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
            <Link
              to="/registration"
              className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-bold py-4 px-12 rounded-lg transition-all duration-300 flex items-center text-xl btn-glow"
            >
              Secure Your Spot Now
              <ArrowRight className="ml-3 h-6 w-6" />
            </Link>
          </div>
          
          {/* Contact Information */}
          <div className="glassmorphism p-8 rounded-xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-6">Contact Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div>
                <h4 className="text-psyco-green-DEFAULT font-semibold mb-2">Email</h4>
                <a 
                  href="mailto:<EMAIL>"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
              <div>
                <h4 className="text-psyco-green-DEFAULT font-semibold mb-2">Phone 1</h4>
                <a 
                  href="tel:+916374344424"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  +91 6374 344 424
                </a>
              </div>
              <div>
                <h4 className="text-psyco-green-DEFAULT font-semibold mb-2">Phone 2</h4>
                <a 
                  href="tel:+916379869678"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  +91 6379 869 678
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Index;
